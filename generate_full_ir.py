#!/usr/bin/env python3
"""
Generate complete Mid-Level IR for the entire project.
This script generates the full IR with fixed file path handling.
"""

import os
import json
import time
from aider_integration_service import AiderIntegrationService

def generate_full_ir():
    """Generate complete Mid-Level IR for the entire project."""
    
    print("🚀 Generating Complete Mid-Level IR...")
    print("=" * 60)
    
    # Initialize the service
    service = AiderIntegrationService()
    
    # Use current directory as project path
    project_path = os.getcwd()
    
    print(f"📁 Project path: {project_path}")
    
    try:
        # Generate IR for the entire project
        print("\n🔍 Starting IR generation...")
        start_time = time.time()
        
        # Get the IR generator
        ir_generator = service._get_ir_generator()
        
        # Get all Python files
        python_files = ir_generator._get_python_files(project_path)
        print(f"   Found {len(python_files)} Python files")
        
        # Analyze each file as a module (with progress tracking)
        modules = []
        total_files = len(python_files)
        
        for i, file_path in enumerate(python_files, 1):
            try:
                rel_path = os.path.relpath(file_path, project_path)
                
                # Show progress every 10 files
                if i % 10 == 0 or i == total_files:
                    print(f"   Progress: {i}/{total_files} files ({i/total_files*100:.1f}%)")
                
                module_info = ir_generator._analyze_module(project_path, file_path, "gpt-3.5-turbo")
                if module_info:
                    modules.append(module_info)
                    
            except Exception as e:
                print(f"   Warning: Error analyzing {rel_path}: {e}")
                continue
        
        print(f"   ✅ Successfully analyzed {len(modules)} modules")
        
        # Calculate inter-module dependencies with the fixed method
        print("   🔗 Calculating module dependencies...")
        ir_generator._calculate_module_dependencies(modules, project_path, "gpt-3.5-turbo")
        
        # Create the complete IR structure
        ir_data = {
            "modules": modules,
            "metadata": {
                "generated_at": time.time(),
                "project_path": project_path,
                "total_modules": len(modules),
                "generator_version": "1.0.0",
                "generation_time_seconds": time.time() - start_time
            }
        }
        
        end_time = time.time()
        generation_time = end_time - start_time
        
        print(f"   ⏱️  Generation completed in {generation_time:.2f} seconds")
        
        # Save to file
        output_file = "complete_mid_level_ir.json"
        print(f"\n💾 Saving complete IR to: {output_file}")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(ir_data, f, indent=2, ensure_ascii=False)
        
        file_size = os.path.getsize(output_file) / (1024 * 1024)  # Size in MB
        print(f"   📊 File size: {file_size:.2f} MB")
        
        # Display comprehensive statistics
        print(f"\n📈 Complete IR Statistics:")
        print("=" * 40)
        print(f"   Total modules: {len(modules)}")
        
        # Calculate entity statistics
        total_entities = sum(len(module['entities']) for module in modules)
        total_functions = sum(len([e for e in module['entities'] if e['type'] == 'function']) for module in modules)
        total_classes = sum(len([e for e in module['entities'] if e['type'] == 'class']) for module in modules)
        total_dependencies = sum(len(module['dependencies']) for module in modules)
        total_loc = sum(module['loc'] for module in modules)
        
        print(f"   Total entities: {total_entities}")
        print(f"     Functions: {total_functions}")
        print(f"     Classes: {total_classes}")
        print(f"   Total dependencies: {total_dependencies}")
        print(f"   Total lines of code: {total_loc:,}")
        
        # Show criticality distribution
        criticality_counts = {'high': 0, 'medium': 0, 'low': 0}
        change_risk_counts = {'high': 0, 'medium': 0, 'low': 0}
        
        for module in modules:
            for entity in module['entities']:
                criticality = entity.get('criticality', 'medium')
                change_risk = entity.get('change_risk', 'medium')
                criticality_counts[criticality] = criticality_counts.get(criticality, 0) + 1
                change_risk_counts[change_risk] = change_risk_counts.get(change_risk, 0) + 1
        
        print(f"\n   📊 Criticality distribution:")
        print(f"     High: {criticality_counts['high']}")
        print(f"     Medium: {criticality_counts['medium']}")
        print(f"     Low: {criticality_counts['low']}")
        
        print(f"\n   ⚠️  Change risk distribution:")
        print(f"     High: {change_risk_counts['high']}")
        print(f"     Medium: {change_risk_counts['medium']}")
        print(f"     Low: {change_risk_counts['low']}")
        
        # Show top modules by complexity
        print(f"\n🏆 Top 10 modules by entity count:")
        sorted_modules = sorted(modules, key=lambda m: len(m['entities']), reverse=True)
        for i, module in enumerate(sorted_modules[:10], 1):
            print(f"   {i:2d}. {module['name']:<30} ({len(module['entities']):3d} entities, {module['loc']:4d} LOC)")
        
        # Show modules with most dependencies
        print(f"\n🔗 Top 10 modules by dependency count:")
        sorted_by_deps = sorted(modules, key=lambda m: len(m['dependencies']), reverse=True)
        for i, module in enumerate(sorted_by_deps[:10], 1):
            deps_str = ", ".join([f"{d['module']}({d['strength']})" for d in module['dependencies'][:3]])
            if len(module['dependencies']) > 3:
                deps_str += f" +{len(module['dependencies'])-3} more"
            print(f"   {i:2d}. {module['name']:<30} ({len(module['dependencies']):2d} deps): {deps_str}")
        
        print(f"\n🎉 Complete Mid-Level IR generation successful!")
        print(f"   Output file: {output_file}")
        print(f"   File size: {file_size:.2f} MB")
        print(f"   Generation time: {generation_time:.2f} seconds")
        
        return ir_data
        
    except Exception as e:
        print(f"❌ Error during complete IR generation: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    generate_full_ir()
